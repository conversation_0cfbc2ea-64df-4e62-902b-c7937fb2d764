import argparse
import numpy as np
import torch
import torch.backends.cudnn as cudnn
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from tensorboardX import SummaryWriter
from torch.utils.data import DataLoader
from torch.nn.modules.loss import CrossEntropyLoss
from torchvision import transforms
from tqdm import tqdm
from skimage.measure import label

from dataloaders.dataset import (SlicGenerator, TwoStreamBatchSampler, ThreeStreamBatchSampler)
from networks.net_factory import BCP_net, net_factory
from utils import losses, ramps, feature_memory, contrastive_losses, val_2d

from skimage.segmentation import slic
from skimage import graph
from collections import deque

from config import parse_args, patients_to_slices


parser = argparse.ArgumentParser()
# /home/<USER>/data/ACDC  /home/<USER>/data/synapse  /home/<USER>/data/isic18
parser.add_argument('--root_path', type=str, default='/home/<USER>/data/ACDC', help='Name of Experiment')
parser.add_argument('--exp', type=str, default='ACDC/SUMix_v2_add_dycon_loss_mask_maxbeta5.0', help='experiment_name')
parser.add_argument('--model', type=str, default='unet', help='model_name')
parser.add_argument('--pre_iterations', type=int, default=10000, help='maximum epoch number to train')
parser.add_argument('--max_iterations', type=int, default=30000, help='maximum epoch number to train')
parser.add_argument('--test_interval', type=int, default=200, help='maximum epoch number to train')
parser.add_argument('--batch_size', type=int, default=24, help='batch_size per gpu')
parser.add_argument('--deterministic', type=int,  default=1, help='whether use deterministic training')
parser.add_argument('--base_lr', type=float,  default=0.01, help='segmentation network learning rate')
parser.add_argument('--patch_size', type=list,  default=[256, 256], help='patch size of network input')
parser.add_argument('--seed', type=int,  default=1337, help='random seed')
parser.add_argument('--num_classes', type=int,  default=4, help='output channel of network')
# label and unlabel
parser.add_argument('--labeled_bs', type=int, default=12, help='labeled_batch_size per gpu')
parser.add_argument('--labelnum', type=int, default=7, help='labeled data')
parser.add_argument('--u_weight', type=float, default=0.5, help='weight of unlabeled pixels')
# costs
# parser.add_argument('--gpu', type=str,  default='4', help='GPU to use')
parser.add_argument('--consistency', type=float, default=0.1, help='consistency')
parser.add_argument('--consistency_rampup', type=float, default=200.0, help='consistency_rampup')
parser.add_argument('--magnitude', type=float,  default='6.0', help='magnitude')
parser.add_argument('--s_param', type=int,  default=6, help='multinum of random masks')

# slic add
parser.add_argument('--num_labels', type=int,  default=20, help='num_labels')
parser.add_argument('--skip_pretrain', type=int,  default=0, help='skip pretraining or not')
parser.add_argument('--temperature', type=float, default=0.8, help='temperature')

parser.add_argument('--train_list', type=str, default='train_BCP.list', help='train_list')  # train_BCP 和 train1.list是一样的
parser.add_argument('--test_list', type=str, default='test_BCP.list', help='test_list')


args = parser.parse_args()
dice_loss = None
CustomDataset = None


import h5py
from scipy.ndimage import zoom
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap

def compute_entropy(predictions):
    """计算预测的熵值作为不确定性度量"""
    # predictions shape: [batch_size, num_classes, height, width]
    # 添加小的epsilon避免log(0)
    epsilon = 1e-8
    predictions = predictions + epsilon

    # 计算熵: -sum(p * log(p))
    entropy = -torch.sum(predictions * torch.log(predictions), dim=1)
    return entropy

def monte_carlo_dropout(model, image, num_samples=20):
    """使用Monte Carlo Dropout估计不确定性"""
    model.train()  # 启用dropout
    predictions = []

    with torch.no_grad():
        for _ in range(num_samples):
            # 在推理时启用dropout
            pred = model(image)
            pred = torch.softmax(pred, dim=1)
            predictions.append(pred.cpu())

    predictions = torch.stack(predictions)  # [num_samples, batch_size, num_classes, height, width]

    # 计算平均预测
    mean_prediction = torch.mean(predictions, dim=0)

    # 计算预测方差作为不确定性
    variance = torch.var(predictions, dim=0)
    uncertainty = torch.mean(variance, dim=1)  # 对类别维度求平均

    # 计算熵不确定性
    entropy_uncertainty = compute_entropy(mean_prediction)

    return mean_prediction, uncertainty, entropy_uncertainty

def draw_uncertainty():
    # 检查CUDA是否可用
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # 加载模型
    model = BCP_net(in_chns=1, class_num=4)
    model = model.to(device)

    # 加载数据
    h5f = h5py.File("code/patient001_frame01_slice_3.h5", 'r')
    image = h5f['image'][:]
    h5f.close()

    # 加载模型权重
    try:
        state = torch.load('model/ACDC/SUMix_v2_add_dycon_loss_mask_7_labeled/self_train/unet_best_model.pth',
                          map_location=device)
        model.load_state_dict(state)
        print("Model loaded successfully!")
    except FileNotFoundError:
        print("Model file not found, using random weights for demonstration")

    # 预处理图像
    x, y = image.shape
    image = zoom(image, (256 / x, 256 / y), order=0)
    print(f"Original image shape: {(x, y)}, Resized to: {image.shape}")

    # 归一化图像
    image = (image - image.min()) / (image.max() - image.min())
    image_tensor = torch.from_numpy(image.astype(np.float32)).unsqueeze(0).unsqueeze(0).to(device)

    print("Computing uncertainty using Monte Carlo Dropout...")

    # 使用Monte Carlo Dropout计算不确定性
    mean_pred, variance_uncertainty, entropy_uncertainty = monte_carlo_dropout(
        model, image_tensor, num_samples=20
    )

    # 转换为numpy数组用于可视化
    image_np = image
    mean_pred_np = mean_pred.squeeze().numpy()
    variance_uncertainty_np = variance_uncertainty.squeeze().numpy()
    entropy_uncertainty_np = entropy_uncertainty.squeeze().numpy()

    # 获取最终预测类别
    predicted_class = np.argmax(mean_pred_np, axis=0)

    print("Creating uncertainty visualizations...")

    # 创建可视化
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Medical Image Segmentation Uncertainty Analysis', fontsize=16, fontweight='bold')

    # 1. 原始图像
    axes[0, 0].imshow(image_np, cmap='gray')
    axes[0, 0].set_title('Original Image', fontsize=14)
    axes[0, 0].axis('off')

    # 2. 预测分割结果
    # 定义颜色映射
    colors = ['black', 'red', 'green', 'blue']  # 对应4个类别
    cmap = ListedColormap(colors)

    im1 = axes[0, 1].imshow(predicted_class, cmap=cmap, vmin=0, vmax=3)
    axes[0, 1].set_title('Predicted Segmentation', fontsize=14)
    axes[0, 1].axis('off')

    # 添加颜色条
    cbar1 = plt.colorbar(im1, ax=axes[0, 1], shrink=0.8)
    cbar1.set_label('Class', fontsize=12)
    cbar1.set_ticks([0, 1, 2, 3])
    cbar1.set_ticklabels(['Background', 'RV', 'Myo', 'LV'])

    # 3. 方差不确定性
    im2 = axes[0, 2].imshow(variance_uncertainty_np, cmap='hot', interpolation='bilinear')
    axes[0, 2].set_title('Variance-based Uncertainty', fontsize=14)
    axes[0, 2].axis('off')
    cbar2 = plt.colorbar(im2, ax=axes[0, 2], shrink=0.8)
    cbar2.set_label('Uncertainty', fontsize=12)

    # 4. 熵不确定性
    im3 = axes[1, 0].imshow(entropy_uncertainty_np, cmap='hot', interpolation='bilinear')
    axes[1, 0].set_title('Entropy-based Uncertainty', fontsize=14)
    axes[1, 0].axis('off')
    cbar3 = plt.colorbar(im3, ax=axes[1, 0], shrink=0.8)
    cbar3.set_label('Entropy', fontsize=12)

    # 5. 叠加不确定性在原图上
    axes[1, 1].imshow(image_np, cmap='gray', alpha=0.7)
    axes[1, 1].imshow(entropy_uncertainty_np, cmap='hot', alpha=0.5, interpolation='bilinear')
    axes[1, 1].set_title('Uncertainty Overlay on Original', fontsize=14)
    axes[1, 1].axis('off')

    # 6. 高不确定性区域
    # 创建高不确定性的二值掩码
    uncertainty_threshold = np.percentile(entropy_uncertainty_np, 90)  # 取前10%最不确定的区域
    high_uncertainty_mask = entropy_uncertainty_np > uncertainty_threshold

    axes[1, 2].imshow(image_np, cmap='gray')
    axes[1, 2].contour(high_uncertainty_mask, levels=[0.5], colors='red', linewidths=2)
    axes[1, 2].set_title('High Uncertainty Regions (Top 10%)', fontsize=14)
    axes[1, 2].axis('off')

    plt.tight_layout()

    # 保存图像到当前目录
    output_path = 'uncertainty_analysis.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Uncertainty analysis saved to: {output_path}")

    # 保存详细的不确定性数据
    uncertainty_data = {
        'original_image': image_np,
        'predicted_segmentation': predicted_class,
        'mean_prediction': mean_pred_np,
        'variance_uncertainty': variance_uncertainty_np,
        'entropy_uncertainty': entropy_uncertainty_np,
        'high_uncertainty_mask': high_uncertainty_mask
    }

    np.savez('uncertainty_data.npz', **uncertainty_data)
    print("Uncertainty data saved to: uncertainty_data.npz")

    # 显示统计信息
    print("\n=== Uncertainty Statistics ===")
    print(f"Variance Uncertainty - Mean: {variance_uncertainty_np.mean():.4f}, Std: {variance_uncertainty_np.std():.4f}")
    print(f"Entropy Uncertainty - Mean: {entropy_uncertainty_np.mean():.4f}, Std: {entropy_uncertainty_np.std():.4f}")
    print(f"High uncertainty regions: {high_uncertainty_mask.sum()} pixels ({100*high_uncertainty_mask.sum()/high_uncertainty_mask.size:.2f}%)")

    plt.show()

if __name__ == "__main__":
    draw_uncertainty()

    


