import numpy as np
import torch
from medpy import metric
from scipy.ndimage import zoom
import pdb
import torch.nn.functional as F


# 在原始代码上做了一点修改，因为在训练2d liver时报了错
def calculate_metric_percase(pred, gt):
    pred[pred > 0] = 1
    gt[gt > 0] = 1

    # 检查预测和真实标签是否都包含正样本
    if pred.sum() > 0 and gt.sum() > 0:
        dice = metric.binary.dc(pred, gt)
        # jc = metric.binary.jc(pred, gt)
        # asd = metric.binary.asd(pred, gt)
        # hd95 = metric.binary.hd95(pred, gt)
        jc = 0
        asd = 0
        hd95 = 0
        return dice, jc, hd95, asd
    elif pred.sum() > 0 and gt.sum() == 0:
        return 1, 1, 0, 0
    else: 
        return 0, 0, 0, 0


def test_single_volume(image, label, model, classes, patch_size=[256, 256]):
    image, label = image.squeeze(0).cpu().detach().numpy(), label.squeeze(0).cpu().detach().numpy()
    prediction = np.zeros_like(label)
    for ind in range(image.shape[0]):
        slice = image[ind, :, :]
        x, y = slice.shape[0], slice.shape[1]
        slice = zoom(slice, (patch_size[0] / x, patch_size[1] / y), order=0)
        input = torch.from_numpy(slice).unsqueeze(0).unsqueeze(0).float().cuda()
        model.eval()
        with torch.no_grad():
            output = model(input)
            if len(output)>1:
                output = output[0]
            out = torch.argmax(torch.softmax(output, dim=1), dim=1).squeeze(0)
            out = out.cpu().detach().numpy()
            pred = zoom(out, (x / patch_size[0], y / patch_size[1]), order=0)
            prediction[ind] = pred
    metric_list = []
    for i in range(1, classes):
        metric_list.append(calculate_metric_percase(prediction == i, label == i))
    return metric_list

def test_single_volume_acc(image, label, model, classes, patch_size=[256, 256]):
    image, label = image.squeeze(0).cpu().numpy(), label.squeeze(0).cpu().numpy()
    D, H, W = image.shape
    
    # resize 到统一 patch_size
    slices = np.stack([zoom(image[i], (patch_size[0] / H, patch_size[1] / W), order=0) for i in range(D)], axis=0)
    input = torch.from_numpy(slices).unsqueeze(1).float().cuda()  # [D,1,H,W]

    model.eval()
    with torch.no_grad():
        output = model(input)  # [D,C,H,W]
        if isinstance(output, (list, tuple)):
            output = output[0]
        out = torch.argmax(torch.softmax(output, dim=1), dim=1).cpu().numpy()  # [D,H,W]

    # resize 回原始大小
    prediction = np.stack([zoom(out[i], (H / patch_size[0], W / patch_size[1]), order=0) for i in range(D)], axis=0)

    # 计算 metric
    metric_list = [calculate_metric_percase(prediction == i, label == i) for i in range(1, classes)]
    return metric_list

def test_single_volume_acc_2(image, label, model, classes, patch_size=[256, 256]):
    """
    image: [1, D, H, W] torch.Tensor
    label: [1, D, H, W] torch.Tensor
    """
    image, label = image.cuda(), label.cuda()
    D, H, W = image.shape[1:]

    # ---- resize 到统一 patch_size (GPU 上做) ----
    # 先转成 [D,1,H,W]
    slices = image.squeeze(0).unsqueeze(1).float()  # [D,1,H,W]
    slices_resized = F.interpolate(slices, size=patch_size, mode="bilinear", align_corners=False)  # [D,1,h,w]

    # ---- 推理 ----
    model.eval()
    with torch.no_grad():
        output = model(slices_resized)  # [D,C,h,w]
        if isinstance(output, (list, tuple)):
            output = output[0]
        out = torch.argmax(torch.softmax(output, dim=1), dim=1)  # [D,h,w]

    # ---- resize 回原始大小 (GPU 上做) ----
    out_resized = F.interpolate(out.unsqueeze(1).float(), size=(H, W), mode="nearest").squeeze(1).to(torch.uint8)  # [D,H,W]

    # ---- 计算 metric ----
    prediction = out_resized.cpu().numpy()
    label = label.squeeze(0).cpu().numpy()
    metric_list = [calculate_metric_percase(prediction == i, label == i) for i in range(1, classes)]
    return metric_list

def test_single_volume_batch_mcnet(image, label, model, classes, patch_size=[256, 256], batch_size=16):
    """
    image: [1, D, H, W] torch.Tensor
    label: [1, D, H, W] torch.Tensor
    """
    image, label = image.cuda(), label.cuda()
    D, H, W = image.shape[1:]

    # ---- resize 到统一 patch_size (GPU 上做) ----
    # 转成 [D,1,H,W]
    slices = image.squeeze(0).unsqueeze(1).float()  # [D,1,H,W]
    slices_resized = F.interpolate(slices, size=patch_size, mode="bilinear", align_corners=False)  # [D,1,h,w]

    # ---- 分 batch 推理 ----
    model.eval()
    preds = []
    with torch.no_grad():
        for start in range(0, D, batch_size):
            end = min(start + batch_size, D)
            batch = slices_resized[start:end]  # [B,1,h,w]
            output = model(batch)  # [B,C,h,w]
            output = output[0][0]
            if isinstance(output, (list, tuple)):
                output = output[0]
            out = torch.argmax(torch.softmax(output, dim=1), dim=1)  # [B,h,w]
            preds.append(out)

    out_all = torch.cat(preds, dim=0)  # [D,h,w]

    # ---- resize 回原始大小 (GPU 上做) ----
    out_resized = F.interpolate(out_all.unsqueeze(1).float(), size=(H, W), mode="nearest").squeeze(1).to(torch.uint8)  # [D,H,W]

    # ---- 计算 metric ----
    prediction = out_resized.cpu().numpy()
    label = label.squeeze(0).cpu().numpy()
    metric_list = [calculate_metric_percase(prediction == i, label == i) for i in range(1, classes)]
    return metric_list

def test_single_volume_batch(image, label, model, classes, patch_size=[256, 256], batch_size=16):
    """
    image: [1, D, H, W] torch.Tensor
    label: [1, D, H, W] torch.Tensor
    """
    image, label = image.cuda(), label.cuda()
    D, H, W = image.shape[1:]

    # ---- resize 到统一 patch_size (GPU 上做) ----
    # 转成 [D,1,H,W]
    slices = image.squeeze(0).unsqueeze(1).float()  # [D,1,H,W]
    slices_resized = F.interpolate(slices, size=patch_size, mode="bilinear", align_corners=False)  # [D,1,h,w]

    # ---- 分 batch 推理 ----
    model.eval()
    preds = []
    with torch.no_grad():
        for start in range(0, D, batch_size):
            end = min(start + batch_size, D)
            batch = slices_resized[start:end]  # [B,1,h,w]
            output = model(batch)  # [B,C,h,w]
            if isinstance(output, (list, tuple)):
                output = output[0]
            out = torch.argmax(torch.softmax(output, dim=1), dim=1)  # [B,h,w]
            preds.append(out)

    out_all = torch.cat(preds, dim=0)  # [D,h,w]

    # ---- resize 回原始大小 (GPU 上做) ----
    out_resized = F.interpolate(out_all.unsqueeze(1).float(), size=(H, W), mode="nearest").squeeze(1).to(torch.uint8)  # [D,H,W]

    # ---- 计算 metric ----
    prediction = out_resized.cpu().numpy()
    label = label.squeeze(0).cpu().numpy()
    metric_list = [calculate_metric_percase(prediction == i, label == i) for i in range(1, classes)]
    return metric_list

def test_single_volume_cml(image, label, model, classes, patch_size=[256, 256]):
    image, label = image.squeeze(0).cpu().detach(
    ).numpy(), label.squeeze(0).cpu().detach().numpy()
    prediction = np.zeros_like(label)
    for ind in range(image.shape[0]):
        slice = image[ind, :, :]
        x, y = slice.shape[0], slice.shape[1]
        slice = zoom(slice, (patch_size[0] / x, patch_size[1] / y), order=0)
        input = torch.from_numpy(slice).unsqueeze(0).unsqueeze(0).float().cuda()
        model.eval()
        with torch.no_grad():
            output1, output2, _, _ = model(input)
            #if len(output)>1:
            #    output = output[0]
            output = (output1 + output2) / 2
            out = torch.argmax(torch.softmax(output, dim=1), dim=1).squeeze(0)
            out = out.cpu().detach().numpy()
            pred = zoom(out, (x / patch_size[0], y / patch_size[1]), order=0)
            prediction[ind] = pred
    metric_list = []
    for i in range(1, classes):
        metric_list.append(calculate_metric_percase(prediction == i, label == i))
    return metric_list

def test_single_volume_cross(image, label, model_l, model_r, classes, patch_size=[256, 256]):
    image, label = image.squeeze(0).cpu().detach(
    ).numpy(), label.squeeze(0).cpu().detach().numpy()
    prediction = np.zeros_like(label)
    for ind in range(image.shape[0]):
        slice = image[ind, :, :]
        x, y = slice.shape[0], slice.shape[1]
        slice = zoom(slice, (patch_size[0] / x, patch_size[1] / y), order=0)
        input = torch.from_numpy(slice).unsqueeze(0).unsqueeze(0).float().cuda()
        model_r.eval()
        model_l.eval()
        with torch.no_grad():
            output_l = model_l(input)
            output_r = model_r(input)
            output = (output_l + output_r) / 2
            if len(output)>1:
                output = output[0]
            out = torch.argmax(torch.softmax(output, dim=1), dim=1).squeeze(0)
            out = out.cpu().detach().numpy()
            pred = zoom(out, (x / patch_size[0], y / patch_size[1]), order=0)
            prediction[ind] = pred
    metric_list = []
    for i in range(1, classes):
        metric_list.append(calculate_metric_percase(prediction == i, label == i))
    return metric_list
