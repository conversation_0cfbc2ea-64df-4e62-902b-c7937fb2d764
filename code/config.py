from dataloaders.dataset import ACDCDataSets, SynapseDataSets

def parse_args(args):
    # /home/<USER>/data/ACDC  /home/<USER>/data/synapse  /home/<USER>/data/isic18
    if 'ACDC' == args.exp.split("/")[0]:
        args.root_path = '/home/<USER>/data/ACDC'
        args.train_list = 'train_slices.list'
        args.test_list = 'val.list'
        args.num_classes = 4
        args.labelnum = 7  # 10%: 7
        args.labeled_num = args.labelnum  # 为兼容SSL4MIS
        args.patch_size = [256, 256]
        args.test_interval = 200
        CustomDataset = ACDCDataSets
    
    elif 'Synapse' == args.exp.split("/")[0]:
        args.root_path = '/home/<USER>/data/synapse'
        args.train_list = 'train.txt'
        args.test_list = 'test_vol.txt'
        args.num_classes = 9
        args.labelnum = 2  # 10%: 2
        args.labeled_num = args.labelnum  # 为兼容SSL4MIS
        args.patch_size = [256, 256]
        args.test_interval = 1500
        CustomDataset = SynapseDataSets

    else:
        raise NotImplementedError(f'Not Implemented Error "{args.exp}"')
    
    return CustomDataset
    

def patients_to_slices(dataset, patiens_num):
    ref_dict = None
    if "ACDC" in dataset:
        ref_dict = {"1": 32, "3": 68, "7": 136,
                    "14": 256, "21": 396, "28": 512, "35": 664, "70": 1312}
    elif "Synapse" in dataset:
        ref_dict = {"2": 256, "4": 522, "20": 2497}
    else:
        raise NotImplementedError(f'Not Implemented Error "{dataset}"')
    return ref_dict[str(patiens_num)]
