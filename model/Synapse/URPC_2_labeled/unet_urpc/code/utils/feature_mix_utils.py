import numpy as np
import torch

def generate_cutmix_mask_clear(input_size=96, hole_size=64, block_size=16):
    assert input_size >= hole_size
    assert hole_size % block_size == 0
    assert input_size % block_size == 0

    # 可选起始位置：确保边界是16的倍数，且hole不超出边界
    valid_starts = np.arange(0, input_size - hole_size + 1, block_size)

    # 随机起点
    z0 = np.random.choice(valid_starts)
    y0 = np.random.choice(valid_starts)
    x0 = np.random.choice(valid_starts)

    # 初始化全 1 mask
    mask = np.ones((input_size, input_size, input_size), dtype=np.uint8)

    # 挖掉一个 cube 洞，设置为 0
    mask[z0:z0+hole_size, y0:y0+hole_size, x0:x0+hole_size] = 0

    return mask, (z0, y0, x0)

def generate_cutmix_mask_loss_mask_clear(img, input_size=96, hole_size=64, block_size=16):
    batch_size, channel, img_x, img_y, img_z = img.shape[0],img.shape[1],img.shape[2],img.shape[3],img.shape[4]
    assert img_x == img_y == img_z == 96
    loss_mask = torch.ones(batch_size, img_x, img_y, img_z).cuda()
    assert input_size >= hole_size
    assert hole_size % block_size == 0
    assert input_size % block_size == 0

    # 可选起始位置：确保边界是16的倍数，且hole不超出边界
    valid_starts = np.arange(0, input_size - hole_size + 1, block_size)

    # 随机起点
    z0 = np.random.choice(valid_starts)
    y0 = np.random.choice(valid_starts)
    x0 = np.random.choice(valid_starts)

    # 初始化全 1 mask
    mask = torch.ones(img_x, img_y, img_z).cuda()

    # 挖掉一个 cube 洞，设置为 0
    mask[z0:z0+hole_size, y0:y0+hole_size, x0:x0+hole_size] = 0
    loss_mask[:, z0:z0+hole_size, y0:y0+hole_size, x0:x0+hole_size] = 0

    return mask.long(), loss_mask.long(), (z0, y0, x0)


def generate_cutmix_mask_loss_mask_clear_two_holes(img, input_size=96, hole_size=64, block_size=16):
    batch_size, channel, img_x, img_y, img_z = img.shape
    # print(hole_size)
    hole_size = int(hole_size)  # 仍然需要转换为整数
    # print(hole_size)
    assert img_x == img_y == img_z == input_size
    assert hole_size % block_size == 0

    # 初始化全1的 mask
    mask = torch.ones(img_x, img_y, img_z).cuda()
    mask_only_big_hole = torch.ones_like(mask)
    loss_mask = torch.ones(batch_size, img_x, img_y, img_z).cuda()
    loss_mask_only_big = torch.ones_like(loss_mask)

    # ---- Step 1: 挖大洞 ----
    valid_starts = np.arange(0, input_size - hole_size + 1, block_size)
    z0 = np.random.choice(valid_starts)
    y0 = np.random.choice(valid_starts)
    x0 = np.random.choice(valid_starts)

    # 更新 mask 和 loss_mask
    mask[z0:z0+hole_size, y0:y0+hole_size, x0:x0+hole_size] = 0
    mask_only_big_hole[z0:z0+hole_size, y0:y0+hole_size, x0:x0+hole_size] = 0
    loss_mask[:, z0:z0+hole_size, y0:y0+hole_size, x0:x0+hole_size] = 0
    loss_mask_only_big[:, z0:z0+hole_size, y0:y0+hole_size, x0:x0+hole_size] = 0

    # ---- Step 2: 挖小洞（不与大洞重叠）----
    small_size = block_size  # 16
    valid_small_starts = np.arange(0, input_size - small_size + 1, block_size)

    all_candidates = [(z, y, x) for z in valid_small_starts
                                 for y in valid_small_starts
                                 for x in valid_small_starts]

    def is_non_overlapping(z1, y1, x1):
        return not (z1 + small_size > z0 and z1 < z0 + hole_size and
                    y1 + small_size > y0 and y1 < y0 + hole_size and
                    x1 + small_size > x0 and x1 < x0 + hole_size)

    non_overlap_candidates = [coord for coord in all_candidates if is_non_overlapping(*coord)]

    if len(non_overlap_candidates) == 0:
        raise RuntimeError("No valid position for non-overlapping small hole.")

    z1, y1, x1 = non_overlap_candidates[np.random.randint(len(non_overlap_candidates))]

    # 挖小洞
    mask[z1:z1+small_size, y1:y1+small_size, x1:x1+small_size] = 0
    loss_mask[:, z1:z1+small_size, y1:y1+small_size, x1:x1+small_size] = 0
    # 注意：**不要改动 loss_mask_only_big**

    return (
        mask.long(),                     # 包含大洞+小洞的mask
        loss_mask.long(),               # batch版本，包含大洞+小洞
        (z0, y0, x0),                   # 大洞坐标
        (z1, y1, x1),                   # 小洞坐标
        mask_only_big_hole.long(),      # 只有大洞的mask
        loss_mask_only_big.long(),      # batch版本，只有大洞的loss mask
    )



if __name__ == "__main__":
    img = torch.randn(1, 1, 96, 96, 96)
    # mask, loss_mask, start_coord = generate_cutmix_mask_loss_mask_clear(img)
    # print("Mask shape:", mask.shape)
    # print("Loss mask shape:", loss_mask.shape)
    # print("Hole start position (z, y, x):", start_coord)

    mask, loss_mask, big_pos, small_pos, only_big, loss_mask_only_big = generate_cutmix_mask_loss_mask_clear_two_holes(img)


    print("大洞坐标:", big_pos)
    print("小洞坐标:", small_pos)
    print("mask包含大+小洞 shape:", mask.shape)
    print("仅包含大洞的 mask shape:", only_big.shape)

    # 可选：验证两个区域是否重叠
    def check_overlap(a_start, a_size, b_start, b_size=16):
        return all([
            a_start[d] + a_size > b_start[d] and b_start[d] + b_size > a_start[d]
            for d in range(3)
        ])
    assert not check_overlap(big_pos, 64, small_pos), "小洞与大洞重叠！"


# mask, start_coord = generate_cutmix_mask_clear()
# print("Mask shape:", mask.shape)
# print("Hole start position (z, y, x):", start_coord)
